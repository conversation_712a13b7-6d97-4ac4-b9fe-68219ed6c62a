2025-08-04 14:33:42 - main - INFO - lifespan:23 - 应用启动中...
2025-08-04 14:33:42 - core.database - INFO - connect:25 - 正在创建dwoutput数据库连接... [用于存储DWOutput相关数据的数据库]
2025-08-04 14:33:42 - core.database - INFO - connect:39 - dwoutput数据库连接池创建成功: welife-dwoutput.rwlb.rds.aliyuncs.com:3306/dwoutput
2025-08-04 14:33:42 - core.database - INFO - connect:42 - 正在创建wedatas数据库连接... [用于存储WeData相关数据的数据库]
2025-08-04 14:33:43 - core.database - INFO - connect:56 - wedatas数据库连接池创建成功: welife-dwoutput.rwlb.rds.aliyuncs.com:3306/wedatas
2025-08-04 14:33:43 - core.database - INFO - connect:59 - 正在创建品质收银数据库连接... [品质收银数据库，用于存储POS相关数据]
2025-08-04 14:33:43 - core.database - INFO - connect:70 - 品质收银数据库连接池创建成功: gp-2zeb28sy9u8c6i667o-master.gpdb.rds.aliyuncs.com:5432/pos_dw
2025-08-04 14:33:43 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-04 14:33:43 - aiomysql - INFO - execute:243 - None
2025-08-04 14:33:43 - core.database - INFO - _test_database_connections:87 - dwoutput数据库连接测试成功: (1, 350497326, 'dwoutput')
2025-08-04 14:33:43 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-04 14:33:43 - aiomysql - INFO - execute:243 - None
2025-08-04 14:33:43 - core.database - INFO - _test_database_connections:94 - wedatas数据库连接测试成功: (1, 350497327, 'wedatas')
2025-08-04 14:33:43 - core.database - INFO - _test_database_connections:99 - 品质收银数据库连接测试成功: {'test_result': 1, 'current_db': 'pos_dw', 'pg_version': 'PostgreSQL 9.4.24 (Greenplum Database 6.3.0 build dev) on x86_64-unknown-linux-gnu, compiled by gcc (GCC) 9.2.1 20200522 (Alibaba 9.2.1-3 2.17), 64-bit compiled on May 17 2023 19:21:42'}
2025-08-04 14:33:43 - main - INFO - lifespan:27 - 数据库连接已建立
2025-08-04 14:51:53 - request - INFO - log_requests:70 - 收到请求: GET http://*************:8000/
2025-08-04 14:51:53 - request - INFO - log_requests:71 - 请求头: {'host': '*************:8000', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/114.0', 'accept-encoding': 'gzip'}
2025-08-04 14:51:53 - request - INFO - log_requests:78 - 请求处理完成: GET http://*************:8000/ - 状态码: 200 - 耗时: 0.001秒
2025-08-04 14:56:29 - request - INFO - log_requests:70 - 收到请求: GET http://*************:8000/
2025-08-04 14:56:29 - request - INFO - log_requests:71 - 请求头: {'host': '*************:8000', 'user-agent': 'Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/110.0', 'accept': '*/*', 'accept-encoding': 'gzip'}
2025-08-04 14:56:29 - request - INFO - log_requests:78 - 请求处理完成: GET http://*************:8000/ - 状态码: 200 - 耗时: 0.000秒
2025-08-04 14:56:47 - request - INFO - log_requests:70 - 收到请求: GET http://*************:8000/favicon.ico
2025-08-04 14:56:47 - request - INFO - log_requests:71 - 请求头: {'host': '*************:8000', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.71 Safari/537.36 SE 2.X MetaSr 1.0', 'accept': '*/*', 'accept-encoding': 'gzip'}
2025-08-04 14:56:47 - request - INFO - log_requests:78 - 请求处理完成: GET http://*************:8000/favicon.ico - 状态码: 404 - 耗时: 0.001秒
2025-08-04 14:56:50 - request - INFO - log_requests:70 - 收到请求: GET http://api.ipify.org/?format=json
2025-08-04 14:56:50 - request - INFO - log_requests:71 - 请求头: {'host': 'api.ipify.org', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* YaBrowser/23.7.0.2534 Yowser/2.5 Safari/537.36'}
2025-08-04 14:56:50 - request - INFO - log_requests:78 - 请求处理完成: GET http://api.ipify.org/?format=json - 状态码: 200 - 耗时: 0.001秒
2025-08-04 16:16:33 - core.database - INFO - disconnect:115 - dwoutput数据库连接池已关闭
2025-08-04 16:16:33 - core.database - INFO - disconnect:120 - wedatas数据库连接池已关闭
2025-08-04 16:16:33 - core.database - INFO - disconnect:124 - 品质收银数据库连接池已关闭
2025-08-04 16:16:33 - main - INFO - lifespan:33 - 应用已关闭
